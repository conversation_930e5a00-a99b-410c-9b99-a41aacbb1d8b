========================================
AIStudio Real-time Log: main
Started: 2025-09-25T00:48:34.551Z
File: app_main_2025-09-24_19-48-34_001.log
========================================

[2025-09-25T00:48:34.818Z] [INFO] AIStudio application started successfully
[2025-09-25T00:48:34.818Z] [INFO] [main] AIStudio application started successfully
[2025-09-25T00:48:34.864Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-25T00:48:35.908Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-25T00:49:07.989Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-24_19-49-07_001.log
[2025-09-25T00:49:07.989Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-25T00:49:10.363Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-25T00:49:10.363Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-25T00:49:11.374Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
