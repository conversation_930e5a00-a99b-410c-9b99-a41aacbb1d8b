import { useCallback, useEffect, useRef } from 'react';
import { useProcessingQueue as useProcessingQueueContext, ProcessingTask } from '../contexts/ProcessingQueueContext';

export interface UseProcessingTaskOptions {
  type: ProcessingTask['type'];
  name: string;
  pipeline?: string;
  sessionId?: string;
  weight?: number;
  autoComplete?: boolean; // Auto-complete when progress reaches 100%
  autoRemove?: boolean; // Auto-remove when completed
}

export interface ProcessingTaskController {
  taskId: string | null;
  updateProgress: (progress: number, description?: string) => void;
  updateStage: (stageName: string, stageProgress?: number) => void;
  setError: (error: string) => void;
  complete: () => void;
  cancel: () => void;
  isActive: boolean;
}

/**
 * Hook for managing a single processing task in the global queue
 */
export function useProcessingTask(options: UseProcessingTaskOptions): ProcessingTaskController {
  const { addTask, updateTask, removeTask, completeTask, state } = useProcessingQueueContext();
  const taskIdRef = useRef<string | null>(null);
  const optionsRef = useRef(options);
  
  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const startTask = useCallback(() => {
    if (taskIdRef.current) return taskIdRef.current;
    
    const taskId = addTask({
      type: options.type,
      name: options.name,
      description: '',
      progress: 0,
      status: 'pending',
      pipeline: options.pipeline,
      sessionId: options.sessionId,
      weight: options.weight || 1
    });
    
    taskIdRef.current = taskId;
    
    // Immediately set to active
    updateTask(taskId, { status: 'active' });
    
    return taskId;
  }, [addTask, updateTask, options]);

  const updateProgress = useCallback((progress: number, description?: string) => {
    if (!taskIdRef.current) {
      startTask();
    }
    
    if (taskIdRef.current) {
      const updates: Partial<ProcessingTask> = { progress: Math.max(0, Math.min(100, progress)) };
      if (description) updates.description = description;
      
      updateTask(taskIdRef.current, updates);
      
      // Auto-complete if enabled and progress reaches 100%
      if (optionsRef.current.autoComplete && progress >= 100) {
        setTimeout(() => complete(), 100);
      }
    }
  }, [updateTask, startTask]);

  const updateStage = useCallback((stageName: string, stageProgress?: number) => {
    if (!taskIdRef.current) {
      startTask();
    }
    
    if (taskIdRef.current) {
      updateTask(taskIdRef.current, { 
        description: stageName,
        ...(stageProgress !== undefined && { progress: Math.max(0, Math.min(100, stageProgress)) })
      });
    }
  }, [updateTask, startTask]);

  const setError = useCallback((error: string) => {
    if (taskIdRef.current) {
      updateTask(taskIdRef.current, { 
        status: 'error', 
        error,
        endTime: new Date()
      });
    }
  }, [updateTask]);

  const complete = useCallback(() => {
    if (taskIdRef.current) {
      completeTask(taskIdRef.current);

      // Auto-remove if enabled
      if (optionsRef.current.autoRemove) {
        setTimeout(() => {
          if (taskIdRef.current) {
            removeTask(taskIdRef.current);
            taskIdRef.current = null;
          }
        }, 3000);
      }
    }
  }, [completeTask, removeTask]);

  const cancel = useCallback(() => {
    if (taskIdRef.current) {
      updateTask(taskIdRef.current, { 
        status: 'cancelled',
        endTime: new Date()
      });
      
      // Remove cancelled tasks after a short delay
      setTimeout(() => {
        if (taskIdRef.current) {
          removeTask(taskIdRef.current);
          taskIdRef.current = null;
        }
      }, 1000);
    }
  }, [updateTask, removeTask]);

  // Check if task is currently active
  const currentTask = taskIdRef.current ? state.tasks.find(t => t.id === taskIdRef.current) : null;
  const isActive = currentTask ? (currentTask.status === 'active' || currentTask.status === 'pending') : false;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't auto-remove on unmount unless explicitly configured
      // This allows tasks to continue in background
    };
  }, []);

  return {
    taskId: taskIdRef.current,
    updateProgress,
    updateStage,
    setError,
    complete,
    cancel,
    isActive
  };
}

/**
 * Hook for managing model download tasks specifically
 */
export function useModelDownloadTask(pipelineName: string, modelName?: string) {
  return useProcessingTask({
    type: 'model-download',
    name: `Downloading ${modelName || 'Models'}`,
    pipeline: pipelineName,
    weight: 2, // Model downloads are typically longer, give them more weight
    autoComplete: true,
    autoRemove: false // Keep completed downloads visible for a while
  });
}

/**
 * Hook for managing 3D generation tasks
 */
export function use3DGenerationTask(pipeline: string, sessionId?: string) {
  return useProcessingTask({
    type: '3d-generation',
    name: 'Generating 3D Model',
    pipeline,
    sessionId,
    weight: 3, // 3D generation is typically the longest process
    autoComplete: true,
    autoRemove: false
  });
}

/**
 * Hook for managing image generation tasks
 */
export function useImageGenerationTask(sessionId?: string) {
  return useProcessingTask({
    type: 'image-generation',
    name: 'Generating Image',
    sessionId,
    weight: 1,
    autoComplete: true,
    autoRemove: false
  });
}

/**
 * Hook for managing video generation tasks
 */
export function useVideoGenerationTask(sessionId?: string) {
  return useProcessingTask({
    type: 'video-generation',
    name: 'Generating Video',
    sessionId,
    weight: 2,
    autoComplete: true,
    autoRemove: false
  });
}

/**
 * Hook for managing upscaling tasks
 */
export function useUpscalingTask(sessionId?: string) {
  return useProcessingTask({
    type: 'upscaling',
    name: 'Upscaling Image',
    sessionId,
    weight: 1,
    autoComplete: true,
    autoRemove: false
  });
}

/**
 * Hook for getting overall processing queue status
 */
export function useProcessingQueueStatus() {
  const { state } = useProcessingQueueContext();
  
  return {
    hasActiveTasks: state.hasActiveTasks,
    overallProgress: state.overallProgress,
    activeTasks: state.tasks.filter(t => t.status === 'active' || t.status === 'pending'),
    completedTasks: state.completedTasks,
    totalTasks: state.tasks.length,
    isModalOpen: state.isModalOpen,
    isMinimized: state.isMinimized
  };
}
