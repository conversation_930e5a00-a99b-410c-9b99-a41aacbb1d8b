import React, { useRef, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, Minimize2, Maximize2, Activity, CheckCircle } from 'lucide-react';
import { useProcessingQueue } from '../contexts/ProcessingQueueContext';
import { useProcessingQueueStatus } from '../hooks/useProcessingQueue';
import MiniProcessingIndicator from './MiniProcessingIndicator';
import ProcessingTaskItem from './ProcessingTaskItem';

interface ProcessingQueueModalProps {
  isDarkMode: boolean;
}

export default function ProcessingQueueModal({ isDarkMode }: ProcessingQueueModalProps) {
  const {
    state,
    setModalState,
    setModalPosition,
    removeTask,
    clearCompletedTasks
  } = useProcessingQueue();

  const {
    hasActiveTasks,
    overallProgress,
    activeTasks,
    completedTasks,
    isModalOpen,
    isMinimized
  } = useProcessingQueueStatus();

  const dragRef = useRef<HTMLDivElement>(null);
  const dragOffsetRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const isDraggingRef = useRef(false);
  const hasMovedRef = useRef(false);

  // Layout constants
  const MINI_SIZE = 60; // px
  const MODAL_WIDTH = 384; // w-96 = 24rem @ 16px
  const MODAL_HEIGHT = 300; // approximate min height for clamping
  const VIEWPORT_PADDING = 10; // px

  // Auto-expand for model downloads (large downloads)
  useEffect(() => {
    const modelDownloadTasks = activeTasks.filter(task => task.type === 'model-download');
    if (modelDownloadTasks.length > 0 && isMinimized) {
      // Auto-expand if there are model downloads and we're minimized
      setModalState(true, false);
    }
  }, [activeTasks, isMinimized, setModalState]);

  // Auto-collapse when all tasks are completed
  useEffect(() => {
    // Only auto-collapse if modal is open and expanded, no active tasks, but has completed tasks
    if (isModalOpen && !isMinimized && !hasActiveTasks && completedTasks.length > 0) {
      const timer = setTimeout(() => {
        // Double-check conditions haven't changed during the delay
        if (!hasActiveTasks && completedTasks.length > 0) {
          setModalState(false, true); // Minimize to show mini indicator
        }
      }, 2000); // 2 second delay to let users see completion

      return () => clearTimeout(timer);
    }
  }, [isModalOpen, isMinimized, hasActiveTasks, completedTasks.length, setModalState]);


  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDraggingRef.current) return;

    // Mark as a drag to suppress click-to-open/minimize
    hasMovedRef.current = true;

    // Clamp position based on current mode (mini vs modal)
    const maxX = isModalOpen ? (window.innerWidth - MODAL_WIDTH) : (window.innerWidth - MINI_SIZE);
    const maxY = isModalOpen ? (window.innerHeight - MODAL_HEIGHT) : (window.innerHeight - MINI_SIZE);

    const newX = Math.max(0, Math.min(maxX, e.clientX - dragOffsetRef.current.x));
    const newY = Math.max(0, Math.min(maxY, e.clientY - dragOffsetRef.current.y));

    setModalPosition(newX, newY);
  }, [setModalPosition, isModalOpen]);

  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;

    // Detach listeners added on mousedown
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove]);

  // Drag listeners are attached on mousedown and removed on mouseup to ensure dragging works


  const handleMiniIndicatorClick = useCallback(() => {
    if (hasMovedRef.current) {
      hasMovedRef.current = false;
      return;
    }
    setModalState(true, false);
  }, [setModalState]);

  const handleMinimize = useCallback(() => {
    setModalState(false, true);
  }, [setModalState]);

  const handleClose = useCallback(() => {
    setModalState(false, true);
  }, [setModalState]);

  const handleRemoveTask = useCallback((taskId: string) => {

    removeTask(taskId);
  }, [removeTask]);

  const handleClearCompleted = useCallback(() => {
    clearCompletedTasks();
  }, [clearCompletedTasks]);


  // Handle dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Begin drag for either mini indicator or modal window
    isDraggingRef.current = true;
    hasMovedRef.current = false;

    // Use current stored position as origin; works for both mini and modal
    dragOffsetRef.current = {
      x: e.clientX - state.modalPosition.x,
      y: e.clientY - state.modalPosition.y,
    };

    // Attach listeners immediately for responsive dragging
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    e.preventDefault();
  }, [state.modalPosition, handleMouseMove, handleMouseUp]);


  // Always render mini indicator (even when idle)
  const miniIndicator = (
    <div
      className="fixed z-[9999] cursor-grab"
      style={{ left: state.modalPosition.x, top: state.modalPosition.y }}
      onMouseDown={handleMouseDown}
    >
      <MiniProcessingIndicator
        isDarkMode={isDarkMode}
        onClick={handleMiniIndicatorClick}
      />
    </div>
  );

  // Don't render modal if minimized or not open
  if (isMinimized || !isModalOpen) {
    return miniIndicator;
  }

  const modalContent = (
    <div className="fixed inset-0 z-[9998] pointer-events-none">
      {/* Modal window */}
      <div
        ref={dragRef}
        className={`absolute w-96 max-h-[500px] rounded-lg shadow-2xl pointer-events-auto ${
          isDarkMode ? 'bg-gray-900 text-white border border-gray-700' : 'bg-white text-gray-900 border border-gray-200'
        }`}
        style={{
          left: (() => {
            const raw = state.modalPosition.x;
            if (raw + MODAL_WIDTH + VIEWPORT_PADDING > window.innerWidth) {
              return Math.max(VIEWPORT_PADDING, raw - (MODAL_WIDTH - MINI_SIZE));
            }
            return raw;
          })(),
          top: (() => {
            const raw = state.modalPosition.y;
            if (raw + MODAL_HEIGHT + VIEWPORT_PADDING > window.innerHeight) {
              return Math.max(VIEWPORT_PADDING, window.innerHeight - MODAL_HEIGHT - VIEWPORT_PADDING);
            }
            return raw;
          })(),
          maxWidth: 'calc(100vw - 20px)',
          maxHeight: 'calc(100vh - 20px)'
        }}
      >

        {/* Header */}
        <div
          className={`flex items-center justify-between p-4 border-b cursor-move ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center gap-2">
            <Activity className={`w-5 h-5 ${
              hasActiveTasks ? 'text-purple-500' : 'text-green-500'
            }`} />
            <h3 className="text-lg font-semibold">Processing Queue</h3>
            {hasActiveTasks && (
              <span className={`text-sm px-2 py-1 rounded-full ${
                isDarkMode ? 'bg-purple-900 text-purple-200' : 'bg-purple-100 text-purple-800'
              }`}>
                {activeTasks.length} active
              </span>
            )}
          </div>

          <div className="flex items-center gap-1">
            <button
              onClick={handleMinimize}
              className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
              title="Minimize"
            >
              <Minimize2 className="w-4 h-4" />
            </button>
            <button
              onClick={handleClose}
              className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Overall Progress */}
        {hasActiveTasks && (
          <div className="p-4 border-b border-gray-700/30">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-gray-400">{Math.round(overallProgress)}%</span>
            </div>
            <div className={`w-full h-2 rounded-full ${
              isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
            }`}>
              <div
                className="h-full rounded-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        )}

        {/* Task List */}
        <div className="flex-1 overflow-y-auto max-h-80">
          {activeTasks.length === 0 && completedTasks.length === 0 ? (
            <div className="p-8 text-center">
              <Activity className={`w-12 h-12 mx-auto mb-3 ${
                isDarkMode ? 'text-gray-600' : 'text-gray-400'
              }`} />
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                No active processing tasks
              </p>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {/* Active Tasks */}
              {activeTasks.map(task => (
                <ProcessingTaskItem
                  key={task.id}
                  task={task}
                  isDarkMode={isDarkMode}
                  onRemove={handleRemoveTask}
                />
              ))}

              {/* Completed Tasks */}
              {completedTasks.length > 0 && (
                <>
                  <div className="flex items-center justify-between pt-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm font-medium text-green-500">
                        Recently Completed
                      </span>
                    </div>
                    <button
                      onClick={handleClearCompleted}
                      className="text-xs text-gray-400 hover:text-gray-300"
                    >
                      Clear
                    </button>
                  </div>
                  {completedTasks.map(task => (
                    <ProcessingTaskItem
                      key={task.id}
                      task={task}
                      isDarkMode={isDarkMode}
                      onRemove={handleRemoveTask}
                      compact
                    />
                  ))}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <>
      {miniIndicator}
      {createPortal(modalContent, document.body)}
    </>
  );
}
