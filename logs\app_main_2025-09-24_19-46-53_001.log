========================================
AIStudio Real-time Log: main
Started: 2025-09-25T00:46:53.765Z
File: app_main_2025-09-24_19-46-53_001.log
========================================

[2025-09-25T00:46:54.032Z] [INFO] AIStudio application started successfully
[2025-09-25T00:46:54.032Z] [INFO] [main] AIStudio application started successfully
[2025-09-25T00:46:54.094Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-25T00:46:55.095Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-25T00:46:55.096Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                    7556 Console                    1 36,425,392 K
[2025-09-25T00:46:55.648Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-25T00:47:32.205Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-24_19-47-32_001.log
[2025-09-25T00:47:32.205Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-25T00:47:34.971Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-25T00:47:34.971Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-25T00:47:35.978Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
